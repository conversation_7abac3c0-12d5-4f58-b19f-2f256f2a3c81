#!/usr/bin/env pwsh
# GameFlex Lambda Hot Reload Deployment Script
# This script deploys Lambda functions using LocalStack CLI with hot reload capability

param(
    [string]$Environment = "development",
    [switch]$Verbose,
    [switch]$BuildOnly,
    [switch]$SkipInstall
)

# Color functions for output
function Write-Status { param([string]$Message) Write-Host "[INFO] $Message" -ForegroundColor Green }
function Write-Warning { param([string]$Message) Write-Host "[WARN] $Message" -ForegroundColor Yellow }
function Write-Error { param([string]$Message) Write-Host "[ERROR] $Message" -ForegroundColor Red }
function Write-Header { param([string]$Message) Write-Host "[LAMBDA-HOT-RELOAD] $Message" -ForegroundColor Cyan }

Write-Header "GameFlex Lambda Hot Reload Deployment"
Write-Host ""

# Set AWS environment variables for LocalStack
$env:AWS_ACCESS_KEY_ID = "test"
$env:AWS_SECRET_ACCESS_KEY = "test"
$env:AWS_DEFAULT_REGION = "us-east-1"
$env:AWS_ENDPOINT_URL = "http://localhost:45660"

# Function to check if LocalStack is running
function Test-LocalStackRunning {
    try {
        Invoke-RestMethod -Uri "http://localhost:45660/_localstack/health" -TimeoutSec 5 -ErrorAction SilentlyContinue | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

# Function to install dependencies for a Lambda function
function Install-LambdaDependencies {
    param([string]$FunctionPath)
    
    $functionName = Split-Path $FunctionPath -Leaf
    Write-Status "Installing dependencies for $functionName..."
    
    Push-Location $FunctionPath
    try {
        if (-not $SkipInstall) {
            npm install
            if ($LASTEXITCODE -ne 0) {
                Write-Error "Failed to install dependencies for $functionName"
                return $false
            }
        }
        return $true
    }
    finally {
        Pop-Location
    }
}

# Function to build a Lambda function with ESbuild (one-time build)
function Build-LambdaFunction {
    param([string]$FunctionPath)
    
    $functionName = Split-Path $FunctionPath -Leaf
    Write-Status "Building $functionName Lambda function..."
    
    Push-Location $FunctionPath
    try {
        # Create dist directory if it doesn't exist
        if (-not (Test-Path "dist")) {
            New-Item -ItemType Directory -Path "dist" -Force | Out-Null
        }
        
        # Run one-time build
        npm run build-once
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Failed to build $functionName"
            return $false
        }
        
        Write-Status "$functionName built successfully"
        return $true
    }
    finally {
        Pop-Location
    }
}

# Function to start hot reload build for a Lambda function
function Start-HotReloadBuild {
    param([string]$FunctionPath)
    
    $functionName = Split-Path $FunctionPath -Leaf
    Write-Status "Starting hot reload build for $functionName..."
    
    Push-Location $FunctionPath
    try {
        # Create dist directory if it doesn't exist
        if (-not (Test-Path "dist")) {
            New-Item -ItemType Directory -Path "dist" -Force | Out-Null
        }
        
        # Start build in watch mode (background process)
        $buildProcess = Start-Process -FilePath "npm.cmd" -ArgumentList "run", "build" -NoNewWindow -PassThru
        Write-Status "Hot reload build started for $functionName (PID: $($buildProcess.Id))"
        
        # Wait a moment for initial build
        Start-Sleep -Seconds 3
        
        return $buildProcess
    }
    finally {
        Pop-Location
    }
}

# Function to deploy a Lambda function with hot reload
function Deploy-LambdaWithHotReload {
    param(
        [string]$FunctionName,
        [string]$FunctionPath,
        [string]$Handler = "index.handler",
        [string]$Description
    )
    
    Write-Status "Deploying $FunctionName with hot reload..."
    
    $distPath = Join-Path $FunctionPath "dist"
    $absoluteDistPath = (Resolve-Path $distPath).Path
    
    # Check if dist directory exists and has index.js
    if (-not (Test-Path (Join-Path $distPath "index.js"))) {
        Write-Error "Built Lambda function not found at $distPath/index.js"
        return $false
    }
    
    # Get Cognito information for environment variables
    $userPoolId = ""
    $userPoolClientId = ""
    
    try {
        $userPools = awslocal cognito-idp list-user-pools --max-results 50 --query 'UserPools[?contains(Name, `GameFlex`) || contains(Name, `gameflex`)].Id' --output text 2>$null
        if ($userPools -and $userPools -ne "None") {
            $userPoolId = ($userPools -split "`t")[0]
            
            if ($userPoolId) {
                $userPoolClients = awslocal cognito-idp list-user-pool-clients --user-pool-id $userPoolId --query 'UserPoolClients[0].ClientId' --output text 2>$null
                if ($userPoolClients -and $userPoolClients -ne "None") {
                    $userPoolClientId = $userPoolClients
                }
            }
        }
    }
    catch {
        Write-Warning "Could not retrieve Cognito information: $_"
    }
    
    # Check if function exists
    $functionExists = $false
    try {
        awslocal lambda get-function --function-name $FunctionName 2>$null | Out-Null
        $functionExists = $true
        Write-Status "Function $FunctionName exists, updating..."
    }
    catch {
        Write-Status "Function $FunctionName does not exist, creating..."
    }
    
    if ($functionExists) {
        # Update existing function with hot reload
        try {
            awslocal lambda update-function-code `
                --function-name $FunctionName `
                --code "S3Bucket=hot-reload,S3Key=$absoluteDistPath" 2>$null | Out-Null
            
            awslocal lambda update-function-configuration `
                --function-name $FunctionName `
                --handler $Handler `
                --environment "Variables={COGNITO_USER_POOL_ID=$userPoolId,COGNITO_USER_POOL_CLIENT_ID=$userPoolClientId,AWS_ENDPOINT_URL=http://host.docker.internal:4566,AWS_DEFAULT_REGION=us-east-1}" 2>$null | Out-Null
            
            Write-Status "Successfully updated $FunctionName with hot reload"
            return $true
        }
        catch {
            Write-Error "Failed to update $FunctionName`: $($_.Exception.Message)"
            return $false
        }
    }
    else {
        # Create new function with hot reload
        try {
            awslocal lambda create-function `
                --function-name $FunctionName `
                --runtime nodejs18.x `
                --role "arn:aws:iam::000000000000:role/lambda-role" `
                --handler $Handler `
                --code "S3Bucket=hot-reload,S3Key=$absoluteDistPath" `
                --description $Description `
                --environment "Variables={COGNITO_USER_POOL_ID=$userPoolId,COGNITO_USER_POOL_CLIENT_ID=$userPoolClientId,AWS_ENDPOINT_URL=http://localhost:45660,AWS_DEFAULT_REGION=us-east-1}" 2>$null | Out-Null
            
            Write-Status "Successfully created $FunctionName with hot reload"
            return $true
        }
        catch {
            Write-Error "Failed to create $FunctionName`: $($_.Exception.Message)"
            return $false
        }
    }
}

# Check if LocalStack is running
if (-not (Test-LocalStackRunning)) {
    Write-Error "LocalStack is not running. Please start LocalStack first."
    exit 1
}

Write-Status "LocalStack is running"

# Ensure IAM role exists for Lambda functions
Write-Status "Ensuring Lambda execution role exists..."
try {
    awslocal iam get-role --role-name lambda-role 2>$null | Out-Null
    Write-Status "Lambda execution role already exists"
}
catch {
    Write-Status "Creating Lambda execution role..."
    
    $trustPolicy = @{
        Version   = "2012-10-17"
        Statement = @(
            @{
                Effect    = "Allow"
                Principal = @{
                    Service = "lambda.amazonaws.com"
                }
                Action    = "sts:AssumeRole"
            }
        )
    } | ConvertTo-Json -Depth 10
    
    $trustPolicy | Out-File -FilePath "temp-trust-policy.json" -Encoding UTF8
    
    awslocal iam create-role --role-name lambda-role --assume-role-policy-document file://temp-trust-policy.json 2>$null | Out-Null
    awslocal iam attach-role-policy --role-name lambda-role --policy-arn arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole 2>$null | Out-Null
    
    Remove-Item "temp-trust-policy.json" -Force -ErrorAction SilentlyContinue
    Write-Status "Lambda execution role created"
}

# Get all Lambda function directories
$lambdaFunctions = Get-ChildItem -Path "lambda-functions" -Directory | Where-Object {
    Test-Path (Join-Path $_.FullName "package.json")
}

if ($lambdaFunctions.Count -eq 0) {
    Write-Warning "No Lambda functions found with package.json"
    exit 0
}

Write-Status "Found $($lambdaFunctions.Count) Lambda functions"

$buildProcesses = @()
$deploymentErrors = @()

# Install dependencies and build functions
foreach ($function in $lambdaFunctions) {
    $functionPath = $function.FullName
    $functionName = $function.Name
    
    Write-Status "Processing $functionName..."
    
    # Install dependencies
    if (-not (Install-LambdaDependencies -FunctionPath $functionPath)) {
        $deploymentErrors += "Failed to install dependencies for $functionName"
        continue
    }
    
    if ($BuildOnly) {
        # Just build once
        if (-not (Build-LambdaFunction -FunctionPath $functionPath)) {
            $deploymentErrors += "Failed to build $functionName"
        }
    }
    else {
        # Start hot reload build
        $buildProcess = Start-HotReloadBuild -FunctionPath $functionPath
        if ($buildProcess) {
            $buildProcesses += @{
                Name    = $functionName
                Process = $buildProcess
                Path    = $functionPath
            }
        }
        else {
            $deploymentErrors += "Failed to start hot reload build for $functionName"
        }
    }
}

if ($BuildOnly) {
    if ($deploymentErrors.Count -gt 0) {
        Write-Warning "Some builds failed:"
        foreach ($errorMsg in $deploymentErrors) {
            Write-Warning "  - $errorMsg"
        }
        exit 1
    }
    else {
        Write-Status "All Lambda functions built successfully"
        exit 0
    }
}

# Wait for initial builds to complete
Write-Status "Waiting for initial builds to complete..."
Start-Sleep -Seconds 5

# Deploy functions with hot reload
foreach ($function in $lambdaFunctions) {
    $functionName = $function.Name
    $functionPath = $function.FullName
    $lambdaFunctionName = "gameflex-$functionName-$Environment"
    
    if (-not (Deploy-LambdaWithHotReload -FunctionName $lambdaFunctionName -FunctionPath $functionPath -Description "GameFlex $functionName functions with hot reload")) {
        $deploymentErrors += "Failed to deploy $functionName"
    }
}

# Report results
if ($deploymentErrors.Count -gt 0) {
    Write-Warning "Some deployments failed:"
    foreach ($errorMsg in $deploymentErrors) {
        Write-Warning "  - $errorMsg"
    }
}
else {
    Write-Status "All Lambda functions deployed successfully with hot reload!"
}

Write-Header "Hot reload deployment completed!"
Write-Status "Lambda functions are now running with hot reload enabled"
Write-Status "Build processes running: $($buildProcesses.Count)"

if ($buildProcesses.Count -gt 0) {
    Write-Host ""
    Write-Host "🔥 Hot reload is active! Your Lambda functions will automatically update when you save changes." -ForegroundColor Yellow
    Write-Host "📝 Edit your TypeScript files in the src/ directories and they will be automatically recompiled and reloaded." -ForegroundColor Yellow
    Write-Host ""
    Write-Status "Build processes (you can stop them with Ctrl+C or by closing this terminal):"
    foreach ($buildInfo in $buildProcesses) {
        Write-Host "  🔧 $($buildInfo.Name) (PID: $($buildInfo.Process.Id))" -ForegroundColor Cyan
    }
}
