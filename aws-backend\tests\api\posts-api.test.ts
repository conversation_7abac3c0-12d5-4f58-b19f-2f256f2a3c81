/**
 * Posts API tests for like/unlike functionality
 * Tests the posts endpoints including like and unlike operations
 */

import { AuthApiClient, PostsApiClient, expectErrorResponse, expectSuccessResponse } from '../utils/api-helpers';

describe('Posts API Like/Unlike Tests', () => {
  let authClient: AuthApiClient;
  let postsClient: PostsApiClient;
  let accessToken: string;
  let testPostId: string;

  beforeAll(async () => {
    authClient = new AuthApiClient();
    postsClient = new PostsApiClient();

    // Sign in to get access token
    const signinResponse = await authClient.signin('<EMAIL>', 'DevPassword123!');
    if (signinResponse.statusCode === 200 && signinResponse.body.access_token) {
      accessToken = signinResponse.body.access_token;
    } else {
      throw new Error('Failed to authenticate for tests');
    }

    // Create a test post for like/unlike operations
    const createPostResponse = await postsClient.createPost(accessToken, {
      content: 'Test post for like/unlike functionality'
    });

    if (createPostResponse.statusCode === 201 && createPostResponse.body.post) {
      testPostId = createPostResponse.body.post.id;
    } else {
      throw new Error('Failed to create test post');
    }
  });

  afterAll(async () => {
    // Clean up: unlike the post if it was liked during tests
    if (testPostId && accessToken) {
      try {
        await postsClient.unlikePost(accessToken, testPostId);
      } catch (error) {
        // Ignore cleanup errors
      }
    }
  });

  describe('POST /posts/{id}/like', () => {
    it('should like a post successfully', async () => {
      const response = await postsClient.likePost(accessToken, testPostId);
      expectSuccessResponse(response, 200);
      expect(response.body.message).toBe('Post liked successfully');
    });

    it('should return error when trying to like the same post twice', async () => {
      // First like should succeed
      await postsClient.likePost(accessToken, testPostId);

      // Second like should fail
      const response = await postsClient.likePost(accessToken, testPostId);
      expectErrorResponse(response, 409, 'Post already liked');
    });

    it('should return error for non-existent post', async () => {
      const fakePostId = '00000000-0000-0000-0000-000000000000';
      const response = await postsClient.likePost(accessToken, fakePostId);
      expectErrorResponse(response, 404, 'Post not found');
    });

    it('should return error without authentication', async () => {
      const response = await postsClient.likePost('', testPostId);
      expectErrorResponse(response, 401, 'Unauthorized');
    });

    it('should return error with invalid token', async () => {
      const response = await postsClient.likePost('invalid-token', testPostId);
      expectErrorResponse(response, 401, 'Unauthorized');
    });

    it('should return error for missing post ID', async () => {
      const response = await postsClient.likePost(accessToken, '');
      expectErrorResponse(response, 400, 'Post ID is required');
    });
  });

  describe('DELETE /posts/{id}/like', () => {
    beforeEach(async () => {
      // Ensure post is liked before each unlike test
      try {
        await postsClient.likePost(accessToken, testPostId);
      } catch (error) {
        // Post might already be liked, ignore error
      }
    });

    it('should unlike a post successfully', async () => {
      const response = await postsClient.unlikePost(accessToken, testPostId);
      expectSuccessResponse(response, 200);
      expect(response.body.message).toBe('Post unliked successfully');
    });

    it('should return error when trying to unlike a post that is not liked', async () => {
      // First unlike should succeed
      await postsClient.unlikePost(accessToken, testPostId);

      // Second unlike should fail
      const response = await postsClient.unlikePost(accessToken, testPostId);
      expectErrorResponse(response, 404, 'Like not found');
    });

    it('should return error for non-existent post', async () => {
      const fakePostId = '00000000-0000-0000-0000-000000000000';
      const response = await postsClient.unlikePost(accessToken, fakePostId);
      expectErrorResponse(response, 404, 'Like not found');
    });

    it('should return error without authentication', async () => {
      const response = await postsClient.unlikePost('', testPostId);
      expectErrorResponse(response, 401, 'Unauthorized');
    });

    it('should return error with invalid token', async () => {
      const response = await postsClient.unlikePost('invalid-token', testPostId);
      expectErrorResponse(response, 401, 'Unauthorized');
    });

    it('should return error for missing post ID', async () => {
      const response = await postsClient.unlikePost(accessToken, '');
      expectErrorResponse(response, 400, 'Post ID is required');
    });
  });

  describe('GET /posts - Like Status Integration', () => {
    it('should include correct like status in posts list', async () => {
      // Unlike the post first to ensure clean state
      try {
        await postsClient.unlikePost(accessToken, testPostId);
      } catch (error) {
        // Ignore if not liked
      }

      // Get posts and verify like status is false
      let response = await postsClient.getPosts(accessToken);
      expectSuccessResponse(response, 200);

      const post = response.body.posts.find((p: any) => p.id === testPostId);
      expect(post).toBeDefined();
      expect(post.is_liked_by_current_user).toBe(false);

      // Like the post
      await postsClient.likePost(accessToken, testPostId);

      // Get posts again and verify like status is true
      response = await postsClient.getPosts(accessToken);
      expectSuccessResponse(response, 200);

      const likedPost = response.body.posts.find((p: any) => p.id === testPostId);
      expect(likedPost).toBeDefined();
      expect(likedPost.is_liked_by_current_user).toBe(true);
    });
  });

  describe('GET /posts/{id} - Like Status Integration', () => {
    it('should include correct like status in single post', async () => {
      // Unlike the post first to ensure clean state
      try {
        await postsClient.unlikePost(accessToken, testPostId);
      } catch (error) {
        // Ignore if not liked
      }

      // Get single post and verify like status is false
      let response = await postsClient.getPost(accessToken, testPostId);
      expectSuccessResponse(response, 200);
      expect(response.body.post.is_liked_by_current_user).toBe(false);

      // Like the post
      await postsClient.likePost(accessToken, testPostId);

      // Get single post again and verify like status is true
      response = await postsClient.getPost(accessToken, testPostId);
      expectSuccessResponse(response, 200);
      expect(response.body.post.is_liked_by_current_user).toBe(true);
    });
  });
});
