/**
 * Posts API tests for like/unlike functionality
 * Tests the posts endpoints including like and unlike operations
 */

import { PostsApiClient, expectErrorResponse, expectSuccessResponse } from '../utils/api-helpers';

describe('Posts API Like/Unlike Tests', () => {
  let postsClient: PostsApiClient;
  let testPostId: string;

  beforeAll(async () => {
    postsClient = new PostsApiClient();

    // Skip authentication for now and use a test post ID
    // In a real scenario, we would authenticate and create a post
    testPostId = 'test-post-id-123';

    console.log('Using test post ID:', testPostId);
  });

  afterAll(async () => {
    // Clean up: unlike the post if it was liked during tests
    if (testPostId) {
      try {
        await postsClient.unlikePost('fake-token', testPostId);
      } catch (error) {
        // Ignore cleanup errors
      }
    }
  });

  describe('POST /posts/{id}/like', () => {
    it('should test like endpoint exists', async () => {
      // Test that the endpoint exists and responds (even if with error due to no auth)
      const response = await postsClient.likePost('fake-token', testPostId);

      // We expect either success or a specific error, not a 404 "endpoint not found"
      expect(response.statusCode).not.toBe(404);
      console.log('Like endpoint response:', response.statusCode, response.body);
    });

    it('should return error without authentication', async () => {
      const response = await postsClient.likePost('', testPostId);
      // Should return 401 or 400, not 404
      expect([400, 401, 403].includes(response.statusCode)).toBe(true);
    });

    it('should return error for missing post ID', async () => {
      const response = await postsClient.likePost('fake-token', '');
      expectErrorResponse(response, 400, 'Post ID is required');
    });
  });

  describe('DELETE /posts/{id}/like', () => {
    it('should test unlike endpoint exists', async () => {
      // Test that the endpoint exists and responds (even if with error due to no auth)
      const response = await postsClient.unlikePost('fake-token', testPostId);

      // We expect either success or a specific error, not a 404 "endpoint not found"
      expect(response.statusCode).not.toBe(404);
      console.log('Unlike endpoint response:', response.statusCode, response.body);
    });

    it('should return error without authentication', async () => {
      const response = await postsClient.unlikePost('', testPostId);
      // Should return 401 or 400, not 404
      expect([400, 401, 403].includes(response.statusCode)).toBe(true);
    });

    it('should return error for missing post ID', async () => {
      const response = await postsClient.unlikePost('fake-token', '');
      expectErrorResponse(response, 400, 'Post ID is required');
    });
  });

  describe('GET /posts - Basic functionality', () => {
    it('should get posts successfully', async () => {
      const response = await postsClient.getPosts();
      expectSuccessResponse(response, 200);
      expect(response.body.posts).toBeDefined();
      expect(Array.isArray(response.body.posts)).toBe(true);
      console.log('Retrieved posts:', response.body.posts.length);
    });
  });
});
